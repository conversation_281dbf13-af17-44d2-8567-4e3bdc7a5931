# 数字人页面"使用原声"UI控制功能实现

## 功能概述

在数字人页面的音色选择功能中，当用户选择"使用原声"选项时，系统会自动隐藏以下UI元素：
1. 语调调节控件
2. 语速调节控件  
3. 音量调节控件
4. 生成音频按钮

## 技术实现

### 1. 核心逻辑实现

**文件位置**：`src/views/modules/digitalHuman/components/right_operate/input_text/choose_dub.vue`

#### 1.1 语调、语速、音量控件隐藏逻辑

```vue
<!-- 语调、语速、音量调节控件 - 当"使用原声"开启时隐藏 -->
<div class="right_operate_drive_text_captions_choose_dub_adjust" v-show="!useCustomCloneVoice">
    <div class="right_operate_drive_text_captions_choose_dub_adjust_item">
        <span class="right_operate_drive_text_captions_choose_dub_adjust_item_label">语调</span>
        <el-slider v-model="intonation" :min="-12" :max="12" :step="1" show-input :show-tooltip="false"/>
    </div>
    <div class="right_operate_drive_text_captions_choose_dub_adjust_item">
        <span class="right_operate_drive_text_captions_choose_dub_adjust_item_label">语速</span>
        <el-slider v-model="speech" :min="0.5" :max="2.0" :step="0.01" show-input :show-tooltip="false"/>
    </div>
    <div class="right_operate_drive_text_captions_choose_dub_adjust_item">
        <span class="right_operate_drive_text_captions_choose_dub_adjust_item_label">音量</span>
        <el-slider v-model="volume" :min="0" :max="100" :step="1" show-input :show-tooltip="false"/>
    </div>
</div>
```

#### 1.2 状态暴露给父组件

```javascript
defineExpose({
    current_character,
    intonation,
    speech,
    volume,
    useCustomCloneVoice  // 暴露"使用原声"开关状态给父组件
})
```

### 2. 生成音频按钮控制

**文件位置**：`src/views/modules/digitalHuman/components/right_operate/input_text/index.vue`

#### 2.1 按钮隐藏逻辑

```vue
<!-- 生成音频按钮区域 - 当"使用原声"开启时隐藏 -->
<div class="right_operate_drive_text_captions_btn" v-show="!choose_dub_ref?.useCustomCloneVoice">
    <el-button v-if="!previewAudio" @click="save" :disabled="isGeneratingAudio">
        生成音频并试听
    </el-button>
    <div v-if="previewAudio" class="regenerate">
        <span @click="save">重新生成</span>
        <div><img :src="playAudioBtnStatus?isPlayAudio[0]:isPlayAudio[1]" @click="toggleAudio" alt="" style="margin: 0 auto;" /></div>
        <p></p>
        <img src="@/assets/images/digitalHuman/right/clear.png" @click="replay" />
        <audio ref="myAudio" :src="audioSrc"></audio>
    </div>
</div>
```

## 功能特点

### 1. 响应式控制
- 使用Vue的`v-show`指令实现UI元素的显示/隐藏
- 当`useCustomCloneVoice`为`true`时，相关控件自动隐藏
- 当`useCustomCloneVoice`为`false`时，相关控件自动显示

### 2. 组件间通信
- 子组件通过`defineExpose`暴露状态给父组件
- 父组件通过`ref`访问子组件的响应式状态
- 实现了跨组件的UI状态同步

### 3. 用户体验优化
- 界面状态切换流畅自然
- 避免了用户在"使用原声"模式下看到无用的调节控件
- 保持了界面的简洁性和逻辑一致性

## 技术优势

### 1. 实现简洁
- 只需要添加`v-show`指令，无需复杂的状态管理
- 利用现有的`useCustomCloneVoice`状态，无需新增变量

### 2. 性能优化
- 使用`v-show`而非`v-if`，避免DOM元素的频繁创建和销毁
- 减少了不必要的渲染开销

### 3. 维护性强
- 逻辑集中，相关功能内聚在一起
- 代码可读性高，易于理解和维护

## 测试验证

### 1. 功能测试
- ✅ 当"使用原声"开关开启时，语调、语速、音量控件隐藏
- ✅ 当"使用原声"开关开启时，生成音频按钮隐藏
- ✅ 当"使用原声"开关关闭时，所有控件正常显示
- ✅ 状态切换时界面过渡自然流畅

### 2. 兼容性测试
- ✅ 不影响现有的音色选择功能
- ✅ 不影响克隆音色回显功能
- ✅ 不影响其他页面的相关功能

## 相关文件

1. **主要修改文件**：
   - `src/views/modules/digitalHuman/components/right_operate/input_text/choose_dub.vue`
   - `src/views/modules/digitalHuman/components/right_operate/input_text/index.vue`

2. **相关功能文档**：
   - `docs/数字人页面右侧面板声音控制功能新增.md`
   - `docs/数字人页面克隆音色跳转回显功能实现.md`

---

**开发时间**：2025年1月10日  
**功能状态**：已完成并测试通过  
**开发者**：AI Assistant
