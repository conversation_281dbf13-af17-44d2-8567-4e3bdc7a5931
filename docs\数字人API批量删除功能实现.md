# 数字人API批量删除功能实现

## 实现概述

在 `src/api/digitalHuman.js` 文件中成功添加了批量删除数字人作品的API接口函数，支持单个删除和批量删除两种场景。

## 功能详情

### 接口信息
- **函数名称**: `batchDeleteDigitalHuman`
- **接口路径**: `/material/digital/batchDeleteDigitalHuman`
- **请求方法**: POST
- **参数格式**: 
  ```javascript
  {
    "ids": [3, 4]  // 数字人ID数组，支持单个或多个ID
  }
  ```

### 代码实现

```javascript
/**
 * 批量删除数字人作品
 *
 * 支持删除单个或多个数字人作品，通过传入数字人ID数组实现批量删除功能
 * 适用于"我的作品"页面的删除操作，既可以单个删除也可以批量删除
 *
 * @param {Object} params - 请求参数对象
 * @param {number[]} params.ids - 数字人ID数组，支持单个或多个ID
 * @returns {Promise} 返回包含删除结果的Promise对象
 */
export const batchDeleteDigitalHuman = (params) => post('/material/digital/batchDeleteDigitalHuman', params)
```

## 使用示例

### 单个删除
```javascript
import { batchDeleteDigitalHuman } from '@/api/digitalHuman'

// 删除ID为3的数字人作品
batchDeleteDigitalHuman({ ids: [3] })
  .then(data => {
    console.log('删除成功:', data);
  })
  .catch(error => {
    console.error('删除失败:', error);
  });
```

### 批量删除
```javascript
import { batchDeleteDigitalHuman } from '@/api/digitalHuman'

// 批量删除ID为3、4、5的数字人作品
batchDeleteDigitalHuman({ ids: [3, 4, 5] })
  .then(data => {
    console.log('批量删除成功:', data);
  })
  .catch(error => {
    console.error('批量删除失败:', error);
  });
```

## 设计特点

1. **统一接口设计**: 使用同一个接口同时支持单个删除和批量删除，简化API设计
2. **参数灵活性**: 通过数组参数支持传入单个或多个ID，使用场景更加灵活
3. **代码风格一致**: 严格按照现有API文件的代码风格和命名规范实现
4. **完整文档**: 提供详细的JSDoc注释和使用示例，便于开发者理解和使用

## 适用场景

- 我的作品页面单个数字人作品删除
- 我的作品页面批量选择删除多个数字人作品
- 数字人管理相关的删除操作

## 技术规范

- 遵循现有API模块的导入导出规范
- 使用统一的POST请求方法
- 保持与其他API函数相同的参数传递方式
- 提供完整的TypeScript类型注释和使用示例

## 实现日期

2025-01-27

## 相关文件

- `src/api/digitalHuman.js` - 主要实现文件
