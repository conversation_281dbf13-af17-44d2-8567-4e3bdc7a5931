# 数字人编辑器名称自动回显功能实现

## 功能概述

在数字人编辑器页面中实现了数字人名称的自动回显功能，当从"我的数字人"或"公共数字人"模块跳转到编辑器页面时，除了显示数字人图片外，还会将数字人的名称自动填充到顶部导航栏的标题输入框中。

## 实现详情

### 1. 数据来源

数字人名称通过以下两种路由参数传递：

1. **digitalHumanData**：JSON字符串格式的完整数字人数据（包含name字段）
2. **digitalHumanName**：直接的数字人名称参数（备用方案）

### 2. 实现位置

在 `DigitalHumanEditorPage.vue` 组件的 `handlePreselectedDigitalHuman` 函数中实现名称回显逻辑。

### 3. 核心实现代码

```javascript
const handlePreselectedDigitalHuman = async () => {
    try {
        // 获取路由参数中的数字人数据
        const digitalHumanDataStr = route.query.digitalHumanData;
        const digitalHumanName = route.query.digitalHumanName;

        // 如果没有完整的数字人数据，但有名称参数，则只设置名称
        if (!digitalHumanDataStr) {
            if (digitalHumanName) {
                await nextTick();
                try {
                    if (headbarRef.value && headbarRef.value.setProjectTitle) {
                        headbarRef.value.setProjectTitle(digitalHumanName);
                        console.log('📝 仅设置数字人名称到标题:', digitalHumanName);
                    }
                } catch (error) {
                    console.warn('设置数字人名称到标题失败:', error);
                }
            }
            return; // 没有完整的数字人数据，返回
        }

        // 解析JSON数据
        let digitalHumanData;
        try {
            digitalHumanData = JSON.parse(digitalHumanDataStr);
        } catch (error) {
            console.error('解析数字人数据失败:', error);
            return;
        }

        // 验证数据完整性
        if (!digitalHumanData.id || !digitalHumanData.url) {
            console.warn('数字人数据不完整:', digitalHumanData);
            return;
        }

        // 设置数字人配置，直接应用到预览区域
        const digitalHumanConfig = {
            type: 'picture',
            url: digitalHumanData.url,
            index: null,
            name: digitalHumanData.name,
            figures_type: digitalHumanData.figuresType
        };

        // 更新当前数字人配置，预览区域将自动显示
        currentDigitalHumanConfig.value = digitalHumanConfig;

        // 🏷️ 设置数字人名称到顶部导航栏标题
        await nextTick();
        try {
            if (headbarRef.value && headbarRef.value.setProjectTitle && digitalHumanData.name) {
                headbarRef.value.setProjectTitle(digitalHumanData.name);
                console.log('📝 已设置数字人名称到标题:', digitalHumanData.name);
            }
        } catch (error) {
            console.warn('设置数字人名称到标题失败:', error);
            // 不影响主流程，继续执行
        }

    } catch (error) {
        console.error('处理预选数字人数据失败:', error);
    }
};
```

### 4. 处理逻辑

#### 4.1 双重数据源支持
- **主要数据源**：`digitalHumanData` JSON字符串中的 `name` 字段
- **备用数据源**：`digitalHumanName` 直接参数

#### 4.2 处理优先级
1. 如果有完整的 `digitalHumanData`，解析其中的 `name` 字段并设置
2. 如果没有 `digitalHumanData` 但有 `digitalHumanName`，直接使用该参数
3. 如果都没有，不进行名称设置

#### 4.3 组件生命周期处理
- 使用 `await nextTick()` 确保顶部导航栏组件已完全初始化
- 通过 `headbarRef.value.setProjectTitle()` 方法设置标题
- 添加完整的错误处理，确保名称设置失败不影响其他功能

### 5. 调用时机

该函数在以下情况下被调用：
- 页面 `onMounted` 生命周期中的 `loadWorkData()` 函数
- 新建模式下（没有作品ID参数时）自动执行

### 6. 用户体验

#### ✅ 完整的名称回显
- 支持从"我的数字人"和"公共数字人"模块跳转时的名称回显
- 名称自动填充到顶部导航栏的标题输入框
- 用户可以进一步编辑修改名称

#### ✅ 多重数据源保障
- 主要通过完整数据JSON获取名称
- 备用通过直接参数获取名称
- 确保在各种情况下都能正确回显

#### ✅ 错误容错机制
- JSON解析失败时的降级处理
- 组件未初始化时的安全检查
- 名称设置失败不影响其他功能

#### ✅ 与现有功能协同
- 与数字人图片显示功能完美配合
- 保持与现有UI设计风格一致
- 不影响编辑器的其他功能

### 7. 技术特点

#### 🔧 组件通信
- 通过 `headbarRef` 引用调用顶部导航栏的方法
- 使用 `setProjectTitle` 方法进行标题设置

#### 🛡️ 安全性
- 完整的参数验证和错误处理
- 防止组件未初始化时的调用错误
- 不影响主要功能的容错机制

#### 📊 调试支持
- 详细的控制台日志记录
- 区分不同数据源的日志标识
- 便于问题排查和功能验证

## 使用场景

1. **从"我的数字人"跳转**：
   - 用户点击"创建视频"按钮
   - 自动跳转到编辑器并回显数字人图片和名称

2. **从"公共数字人"跳转**：
   - 用户选择公共数字人创建视频
   - 自动跳转到编辑器并回显数字人图片和名称

3. **名称编辑**：
   - 回显的名称可以进一步编辑修改
   - 支持标题的保存和管理

## 兼容性

- 与现有的数字人图片显示功能完全兼容
- 不影响编辑器的其他功能
- 支持新建模式和编辑模式
- 向后兼容，不影响没有名称参数的跳转

## 测试建议

1. 测试从"我的数字人"跳转时的名称回显
2. 测试从"公共数字人"跳转时的名称回显
3. 验证名称可以正常编辑和保存
4. 测试数据解析失败时的降级处理
5. 确认与数字人图片显示功能的协同工作
