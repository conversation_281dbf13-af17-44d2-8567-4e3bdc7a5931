# 数字人中转页面"我的数字人"模块集成实现

## 概述

在 `DigitalHumanTransition.vue` 组件中成功集成了 `getDigitalHumanListByUserId` 接口，实现了"我的数字人"模块的真实数据获取和展示功能。

## 实现功能

### 1. 接口集成
- 导入并调用 `src/api/digitalHuman.js` 中的 `getDigitalHumanListByUserId` 接口
- 在组件初始化时（onMounted）自动调用接口获取用户数字人列表
- 设置默认显示条数为 7 条数字人记录

### 2. 数据处理
- 将API返回的数据转换为组件期望的格式
- 保持现有的数据结构：`{ id, progress, status, name, originalData }`
- 确保只显示前7条数据记录

### 3. 状态管理
- 添加加载状态：`myDigitalHumansLoading`
- 添加错误状态：`myDigitalHumansError`
- 实现完整的加载和错误处理流程

### 4. 用户体验优化
- 加载时显示"正在加载我的数字人..."提示
- 错误时显示错误信息和重试按钮
- 保持现有的重命名、删除等交互功能

## 技术实现细节

### 接口调用
```javascript
const getMyDigitalHumansList = async () => {
    try {
        myDigitalHumansLoading.value = true
        myDigitalHumansError.value = null
        
        const userId = getUserId()
        const response = await getDigitalHumanListByUserId({
            page: 1,
            size: 7,
            userId: userId
        })
        
        // 数据转换和处理逻辑
        // ...
    } catch (error) {
        myDigitalHumansError.value = error.message || '获取数字人列表失败'
    } finally {
        myDigitalHumansLoading.value = false
    }
}
```

### 数据转换
- 将API返回的数字人数据映射为组件期望的格式
- 保存原始数据以备后续功能使用
- 处理缺失字段的默认值设置

### UI状态处理
- 使用 `v-if` 条件渲染实现加载、错误、正常三种状态的切换
- 添加对应的CSS样式确保良好的用户体验

## 文件修改记录

### 主要修改
1. **导入接口**：添加 `getDigitalHumanListByUserId` 接口导入
2. **状态变量**：添加 `myDigitalHumansLoading` 和 `myDigitalHumansError` 响应式变量
3. **数据获取函数**：创建 `getMyDigitalHumansList` 函数处理数据获取和转换
4. **生命周期集成**：在 `onMounted` 中调用数据获取函数
5. **模板更新**：添加加载状态和错误状态的UI展示
6. **样式添加**：为加载和错误状态添加对应的CSS样式

### 兼容性保证
- 保持现有的 `digitalHumansProgress` 数据结构不变
- 保留所有现有的交互功能（重命名、删除、悬停效果等）
- 错误时保持默认数据以确保页面正常显示

## 使用说明

### 正常流程
1. 页面加载时自动获取用户的数字人列表
2. 显示前7条数字人记录
3. 支持现有的所有交互功能

### 异常处理
1. 网络错误或接口异常时显示错误信息
2. 提供重试按钮允许用户手动重新获取数据
3. 用户ID不存在时显示相应错误提示

## 注意事项

1. **数据条数限制**：严格控制只显示7条数字人记录
2. **用户ID验证**：确保用户已登录且有有效的用户ID
3. **错误兜底**：发生错误时保持页面基本功能可用
4. **性能考虑**：避免重复调用接口，合理使用加载状态

## 数据结构适配更新

### 接口返回数据结构调整
根据实际接口返回的数据结构，进行了以下调整：

1. **数据获取路径修改**：从 `response.data.list` 改为 `response.records`
2. **图片URL字段映射**：添加 `picUrl` 字段映射，支持动态显示数字人头像
3. **图片加载错误处理**：添加图片加载失败的兜底机制

### 实际数据结构
```javascript
{
  records: [
    {
      id: 3,
      name: "11212",
      picUrl: "https://res.chanjing.cc/chanjing/dp/output/2025-08-01/931b449e494baee976cdeaebce92079a3509bcf4-cover.png",
      // ... 其他字段
    }
  ]
}
```

### 转换后的数据格式
```javascript
{
  id: 3,
  progress: 0,
  status: 'normal',
  name: "11212",
  picUrl: "https://res.chanjing.cc/chanjing/dp/output/2025-08-01/...",
  originalData: { /* 原始API数据 */ }
}
```

### 图片显示逻辑
- 优先使用API返回的 `picUrl`
- 如果 `picUrl` 为空或加载失败，自动使用默认图片 `@/assets/img/ceshi1.png`
- 添加 `@error` 事件处理图片加载异常

## 状态处理功能增强

### 数字人状态管理
根据"我的作品"模块的状态处理逻辑，实现了完整的数字人状态管理：

#### 状态映射规则
- **状态值 `0`**：生成中状态 (`'generating'`)
- **状态值 `1`**：成功/正常状态 (`'normal'`)
- **状态值 `2`**：失败状态 (`'failed'`)

#### 状态显示逻辑
```javascript
// 状态映射函数
const mapDigitalHumanStatus = (apiStatus) => {
    switch (String(apiStatus)) {
        case '0': return 'generating' // 生成中
        case '1': return 'normal'     // 成功/正常
        case '2': return 'failed'     // 失败
        default: return 'normal'      // 默认为正常状态
    }
}
```

#### 生成中状态更新机制
- 自动检测生成中的数字人
- 每30秒调用 `updateWorksStatus` 接口更新状态
- 与"我的作品"模块保持一致的更新频率
- 所有数字人生成完成后自动清除定时器

#### UI状态展示
1. **正常状态**：显示数字人头像和操作菜单
2. **生成中状态**：显示动画和进度百分比
3. **失败状态**：显示失败提示和重新生成按钮

### 技术实现细节

#### 数据转换增强
```javascript
const convertedList = apiList.map((item, index) => {
    const mappedStatus = mapDigitalHumanStatus(item.status)
    return {
        id: item.id || (index + 1),
        progress: mappedStatus === 'generating' ? (item.progress || 0) : 0,
        status: mappedStatus, // 使用映射后的状态
        name: item.name || `数字人${index + 1}`,
        picUrl: item.picUrl || '',
        originalData: item
    }
})
```

#### 状态更新流程
1. 页面加载时检测生成中的数字人
2. 启动定时器每30秒更新状态
3. 调用 `updateWorksStatus` 接口获取最新状态
4. 更新本地数据并重新渲染
5. 无生成中数字人时自动清除定时器

#### 组件生命周期管理
- `onMounted`：启动数据获取和状态更新
- `onUnmounted`：清理定时器防止内存泄漏

## CSS布局优化

### 数字人网格布局调整
针对API返回数据少于7条时的布局问题，进行了以下优化：

#### 问题描述
- 原有样式使用 `justify-content: space-between`
- 数据不足7条时，数字人项目会分散排列在容器两端
- 影响视觉效果和用户体验

#### 解决方案
```scss
.humans-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: flex-start; // 从左往右依次排列，保持固定间距
    gap: 20px;
    width: 1535px;
}
```

#### 优化效果
1. **统一布局**：无论数据多少，都从左往右依次排列
2. **固定间距**：保持 `gap: 20px` 的一致间距
3. **响应式兼容**：移动端同样使用 `flex-start` 布局
4. **用户体验**：符合用户从左到右的阅读习惯

#### 响应式设计调整
```scss
@media (max-width: 768px) {
    .humans-grid {
        justify-content: flex-start; // 移动端也使用从左往右排列
        gap: 12px;
        width: 100%; // 移动端使用全宽
    }
}
```

## 失败状态UI样式优化

### 设计要求匹配
根据提供的设计图和"我的作品"模块的样式规范，对失败状态进行了完整的UI重构：

#### 设计图分析
- 数字人头像正常显示
- 半透明遮罩层覆盖
- 左下角红色"失败"标签
- 中间白色边框的操作按钮
- 右上角删除图标

#### 实现方案
```html
<!-- 失败状态模板结构 -->
<div class="failed-overlay" v-if="item.status === 'failed'">
    <!-- 删除图标 -->
    <img src="@/assets/img/delete.png" @click="deleteDigitalHuman(index)" class="delete-icon" />
    <!-- 失败标签 -->
    <div class="failure-label">失败</div>
</div>
```

#### 样式规范
```scss
.failed-overlay {
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    background: rgba(0, 0, 0, 0.4); // 半透明遮罩
    border-radius: 8px;

    .delete-icon {
        position: absolute;
        top: 8px; right: 8px;
        width: 16px; height: 16px;
        cursor: pointer;
    }

    // 注意：重新编辑按钮已移除，失败状态只保留删除功能

    .failure-label {
        position: absolute;
        bottom: 7px; left: 7px;
        width: 40px; height: 21px;
        background: #FF2D55; // 项目标准红色
        border-radius: 2px;
        font-size: 12px; color: #FFFFFF;
        text-align: center; line-height: 21px;
    }
}
```

#### 一致性保证
- **颜色规范**：使用与"我的作品"模块相同的 `#FF2D55` 红色
- **尺寸规范**：按钮、标签、图标尺寸完全一致
- **交互效果**：悬停效果和点击反馈保持统一
- **字体规范**：使用项目标准字体 `'Alibaba PuHuiTi 2.0'`

#### 功能特性
1. **视觉一致性**：与"我的作品"模块失败状态完全一致
2. **交互简化**：只保留删除操作，移除重新编辑功能
3. **用户体验**：清晰的失败状态标识和删除操作指引
4. **响应式适配**：在不同屏幕尺寸下正常显示

## 交互逻辑优化

### 鼠标悬停状态控制
针对不同数字人状态的悬停交互进行了优化：

#### 问题描述
- 失败状态和生成中状态的数字人在鼠标悬停时仍显示"创建视频"按钮
- 这在逻辑上不合理，因为非正常状态的数字人无法用于创建视频

#### 解决方案
```javascript
// 显示条件优化 - 模板部分
<div class="create-video-overlay" :class="{
    show: digitalHumansHoveredIndex === index && !dropdownVisible && item.status === 'normal'
}">创建视频</div>

// 悬停逻辑优化 - JavaScript部分
const showDigitalHumansCreateVideo = (index) => {
    if (dropdownVisible.value) return

    // 检查数字人状态，只有正常状态才显示创建视频按钮
    const digitalHuman = digitalHumansProgress.value[index]
    if (!digitalHuman || digitalHuman.status !== 'normal') {
        return // 失败状态、生成中状态不显示按钮
    }

    digitalHumansHoveredIndex.value = index
}
```

#### 交互规则
| 数字人状态 | 悬停效果 | 说明 |
|------------|----------|------|
| `'normal'` | 显示"创建视频"按钮 | 正常状态可以创建视频 |
| `'failed'` | 不显示任何悬停按钮 | 失败状态无法创建视频 |
| `'generating'` | 不显示任何悬停按钮 | 生成中状态无法创建视频 |

#### 用户体验改进
1. **逻辑一致性**：只有可用的数字人才显示创建视频选项
2. **视觉清晰性**：避免用户对不可用功能产生困惑
3. **交互合理性**：符合用户对不同状态的预期行为

## 后续优化建议

1. 可以考虑添加数据缓存机制减少接口调用
2. 可以实现数据的实时更新功能
3. 可以添加更详细的错误分类和处理
4. 可以考虑添加数据为空时的友好提示
5. 可以添加图片预加载机制提升用户体验
6. 可以优化失败状态的重新生成功能，支持直接跳转到编辑页面
7. 可以考虑添加数字人项目的拖拽排序功能
8. 可以添加失败原因的详细提示功能
9. 可以为不同状态添加更多的悬停提示信息
