# 我的数字人创建视频跳转功能实现

## 功能概述

在"我的数字人"模块中实现了点击"创建视频"按钮跳转到数字人编辑器页面的功能，支持将当前数字人的图片和相关数据传递到编辑器页面。

## 实现详情

### 1. 添加点击事件

在 `DigitalHumanTransition.vue` 组件的"我的数字人"区域，为"创建视频"按钮添加了点击事件：

```vue
<div class="create-video-overlay" 
     :class="{ show: digitalHumansHoveredIndex === index && !dropdownVisible && item.status === 'normal' }" 
     @click="navigateToMyDigitalHumanEditor(item)">
  创建视频
</div>
```

### 2. 创建专用跳转函数

实现了 `navigateToMyDigitalHumanEditor` 函数，专门处理"我的数字人"的跳转逻辑：

```javascript
const navigateToMyDigitalHumanEditor = (item) => {
    const fromPage = router.currentRoute.value.path

    // 将我的数字人数据格式转换为编辑器期望的格式
    const digitalHumanData = {
        id: item.id,
        name: item.name,
        // 我的数字人使用picUrl字段，需要转换为figures格式以兼容编辑器
        figures: [{
            type: 'image',
            cover: item.picUrl || ''
        }],
        url: item.picUrl || '',
        figuresType: 'image'
    }

    router.push({
        path: '/digital-human-editor-page',
        query: {
            from: fromPage,
            digitalHumanId: item.id,
            digitalHumanName: item.name,
            digitalHumanData: JSON.stringify(digitalHumanData)
        }
    })
}
```

### 3. 数据格式适配

#### 我的数字人数据格式：
- `id`: 数字人ID
- `name`: 数字人名称  
- `picUrl`: 数字人图片URL（直接字段）

#### 编辑器期望格式：
- `id`: 数字人ID
- `name`: 数字人名称
- `figures`: 图片数组格式
- `url`: 主图片URL
- `figuresType`: 图片类型

#### 格式转换逻辑：
将 `picUrl` 字段转换为 `figures` 数组格式，确保与数字人编辑器的数据接收格式兼容。

### 4. 路由参数传递

跳转时传递的查询参数：
- `from`: 来源页面路径（用于返回）
- `digitalHumanId`: 数字人ID
- `digitalHumanName`: 数字人名称
- `digitalHumanData`: JSON字符串格式的完整数字人数据

## 功能特点

### ✅ 完整的数据传递
- 支持传递数字人的ID、名称、图片等完整信息
- 数据格式与编辑器完全兼容

### ✅ 状态控制
- 只有在正常状态（`status === 'normal'`）的数字人才显示"创建视频"按钮
- 生成中和失败状态的数字人不显示此按钮

### ✅ 用户体验
- 悬停显示按钮，点击即可跳转
- 保持与公共数字人模块一致的交互体验

### ✅ 调试支持
- 添加了详细的控制台日志，便于调试和问题排查

## 使用场景

1. **用户在"我的数字人"列表中**
2. **悬停到正常状态的数字人卡片**
3. **点击"创建视频"按钮**
4. **自动跳转到数字人编辑器页面**
5. **编辑器页面接收到完整的数字人数据**

## 技术实现

- **组件**: `DigitalHumanTransition.vue`
- **函数**: `navigateToMyDigitalHumanEditor`
- **路由**: `/digital-human-editor-page`
- **数据传递**: URL查询参数 + JSON字符串

## 兼容性

- 与现有公共数字人跳转逻辑保持一致
- 数据格式完全兼容数字人编辑器页面
- 支持返回功能（通过 `from` 参数）

## 测试建议

1. 测试正常状态数字人的跳转功能
2. 验证传递的数据在编辑器页面是否正确接收
3. 测试返回功能是否正常工作
4. 确认生成中和失败状态不显示"创建视频"按钮
